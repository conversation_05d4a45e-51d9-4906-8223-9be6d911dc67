import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import argparse
from datetime import datetime

# Document processing libraries
from docx import Document
import PyPDF2
import json


class DocumentPageCounter:
    """A class to count pages in DOCX and PDF documents."""

    def __init__(self):
        self.supported_extensions = {'.docx', '.pdf'}
        self.results = []
        self.errors = []

    def count_pdf_pages(self, file_path: Path) -> int:
        """Count pages in a PDF file."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                return len(pdf_reader.pages)
        except Exception as e:
            self.errors.append(f"Error reading PDF {file_path}: {str(e)}")
            return 0

    def count_docx_pages(self, file_path: Path) -> int:
        """Count pages in a DOCX file."""
        try:
            doc = Document(file_path)
            # DOCX doesn't have a direct page count, so we estimate based on content
            # This is an approximation - actual page count depends on formatting

            total_paragraphs = len(doc.paragraphs)
            total_tables = len(doc.tables)

            # Rough estimation: assume ~25 paragraphs per page
            # and each table takes about 1/4 page
            estimated_pages = max(1, (total_paragraphs + total_tables * 6) // 25)

            # Try to get actual page count from document properties if available
            try:
                core_props = doc.core_properties
                if hasattr(core_props, 'pages') and core_props.pages:
                    return core_props.pages
            except:
                pass

            return estimated_pages

        except Exception as e:
            self.errors.append(f"Error reading DOCX {file_path}: {str(e)}")
            return 0

    def count_pages_in_file(self, file_path: Path) -> Tuple[int, str]:
        """Count pages in a single file and return (page_count, file_type)."""
        extension = file_path.suffix.lower()

        if extension == '.pdf':
            return self.count_pdf_pages(file_path), 'PDF'
        elif extension == '.docx':
            return self.count_docx_pages(file_path), 'DOCX'
        else:
            return 0, 'UNSUPPORTED'

    def scan_directory(self, directory_path: Path, recursive: bool = True) -> None:
        """Scan directory for DOCX and PDF files and count their pages."""
        if not directory_path.exists():
            print(f"Error: Directory '{directory_path}' does not exist.")
            return

        if not directory_path.is_dir():
            print(f"Error: '{directory_path}' is not a directory.")
            return

        print(f"Scanning directory: {directory_path}")
        print(f"Recursive scan: {recursive}")
        print("-" * 60)

        # Get all files
        if recursive:
            files = list(directory_path.rglob('*'))
        else:
            files = list(directory_path.iterdir())

        # Filter for supported file types
        document_files = [f for f in files if f.is_file() and f.suffix.lower() in self.supported_extensions]

        if not document_files:
            print("No DOCX or PDF files found in the specified directory.")
            return

        print(f"Found {len(document_files)} document files to process...")
        print()

        total_pages = 0
        file_count_by_type = {'PDF': 0, 'DOCX': 0}
        pages_by_type = {'PDF': 0, 'DOCX': 0}

        for file_path in sorted(document_files):
            try:
                page_count, file_type = self.count_pages_in_file(file_path)

                # Calculate relative path for cleaner display
                try:
                    relative_path = file_path.relative_to(directory_path)
                except ValueError:
                    relative_path = file_path

                file_size = file_path.stat().st_size
                file_size_mb = file_size / (1024 * 1024)

                result = {
                    'file_path': str(relative_path),
                    'full_path': str(file_path),
                    'file_type': file_type,
                    'page_count': page_count,
                    'file_size_mb': round(file_size_mb, 2),
                    'status': 'SUCCESS' if page_count > 0 else 'ERROR'
                }

                self.results.append(result)

                if page_count > 0:
                    total_pages += page_count
                    file_count_by_type[file_type] += 1
                    pages_by_type[file_type] += page_count

                # Print progress
                status_icon = "✓" if page_count > 0 else "✗"
                print(f"{status_icon} {relative_path}")
                print(f"   Type: {file_type}, Pages: {page_count}, Size: {file_size_mb:.2f} MB")
                print()

            except Exception as e:
                error_msg = f"Unexpected error processing {file_path}: {str(e)}"
                self.errors.append(error_msg)
                print(f"✗ Error processing {file_path}: {str(e)}")
                print()

        # Print summary
        self.print_summary(total_pages, file_count_by_type, pages_by_type)

    def print_summary(self, total_pages: int, file_count_by_type: Dict[str, int], pages_by_type: Dict[str, int]) -> None:
        """Print a summary of the page counting results."""
        print("=" * 60)
        print("SUMMARY REPORT")
        print("=" * 60)
        print(f"Total files processed: {len(self.results)}")
        print(f"Total pages counted: {total_pages}")
        print()

        print("By file type:")
        for file_type in ['PDF', 'DOCX']:
            if file_count_by_type[file_type] > 0:
                avg_pages = pages_by_type[file_type] / file_count_by_type[file_type]
                print(f"  {file_type}: {file_count_by_type[file_type]} files, {pages_by_type[file_type]} pages (avg: {avg_pages:.1f} pages/file)")

        if self.errors:
            print(f"\nErrors encountered: {len(self.errors)}")
            for error in self.errors:
                print(f"  - {error}")

        print("\n" + "=" * 60)

    def export_report(self, output_file: Path) -> None:
        """Export detailed report to JSON file."""
        report_data = {
            'scan_timestamp': datetime.now().isoformat(),
            'total_files': len(self.results),
            'total_pages': sum(r['page_count'] for r in self.results),
            'files': self.results,
            'errors': self.errors
        }

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            print(f"Detailed report exported to: {output_file}")
        except Exception as e:
            print(f"Error exporting report: {str(e)}")


def main():
    """Main function to run the document page counter."""
    parser = argparse.ArgumentParser(
        description="Count pages in DOCX and PDF files in a directory",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Scan current directory recursively
  python main.py files                    # Scan 'files' directory recursively
  python main.py files --no-recursive     # Scan 'files' directory only (no subdirs)
  python main.py files --output report.json  # Export detailed report to JSON
        """
    )

    parser.add_argument(
        'directory',
        nargs='?',
        default='.',
        help='Directory to scan for documents (default: current directory)'
    )

    parser.add_argument(
        '--no-recursive',
        action='store_true',
        help='Do not scan subdirectories recursively'
    )

    parser.add_argument(
        '--output',
        '-o',
        type=str,
        help='Export detailed report to JSON file'
    )

    args = parser.parse_args()

    # Convert directory path to Path object
    directory_path = Path(args.directory).resolve()

    # Create counter instance and scan
    counter = DocumentPageCounter()
    counter.scan_directory(directory_path, recursive=not args.no_recursive)

    # Export report if requested
    if args.output:
        output_path = Path(args.output)
        counter.export_report(output_path)


if __name__ == "__main__":
    main()
