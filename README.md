# Document Page Counter

A Python program that counts pages in DOCX and PDF files within a directory and generates detailed reports.

## Features

- **Multi-format support**: Counts pages in both PDF and DOCX files
- **Recursive scanning**: Can scan directories and all subdirectories
- **Detailed reporting**: Shows file-by-file results with page counts and file sizes
- **Summary statistics**: Provides totals and averages by file type
- **JSON export**: Can export detailed results to JSON for further analysis
- **Error handling**: Gracefully handles corrupted or unreadable files

## Installation

This project uses `uv` for dependency management. Make sure you have `uv` installed, then run:

```bash
uv sync
```

## Usage

### Basic Usage

```bash
# Scan current directory recursively
uv run python main.py

# Scan a specific directory recursively
uv run python main.py files

# Scan a directory without recursion (only direct files)
uv run python main.py files --no-recursive
```

### Export to JSON

```bash
# Generate a detailed JSON report
uv run python main.py files --output report.json
```

### Command Line Options

- `directory` (optional): Directory to scan (default: current directory)
- `--no-recursive`: Don't scan subdirectories
- `--output`, `-o`: Export detailed report to JSON file
- `--help`: Show help message

## Output

The program provides:

1. **Console output**: Real-time progress with file-by-file results
2. **Summary report**: Total files, pages, and statistics by file type
3. **JSON export** (optional): Detailed machine-readable report

### Sample Output

```
Scanning directory: C:\Projects\AI\document_page_count\files
Recursive scan: True
------------------------------------------------------------
Found 92 document files to process...

✓ 26596_IPO_C_CPR\document1.pdf
   Type: PDF, Pages: 96, Size: 6.60 MB

✓ 26596_IPO_C_CPR\document2.docx
   Type: DOCX, Pages: 88, Size: 28.16 MB

============================================================
SUMMARY REPORT
============================================================
Total files processed: 92
Total pages counted: 8516

By file type:
  PDF: 58 files, 6217 pages (avg: 107.2 pages/file)
  DOCX: 34 files, 2299 pages (avg: 67.6 pages/file)
============================================================
```

## Technical Notes

### PDF Page Counting
- Uses PyPDF2 to read PDF metadata and count actual pages
- Handles encrypted and corrupted PDFs gracefully

### DOCX Page Counting
- DOCX files don't have explicit page counts in their metadata
- The program attempts to read page count from document properties when available
- Falls back to content-based estimation (paragraphs and tables) when needed
- Estimation assumes ~25 paragraphs per page and tables taking ~1/4 page each

### Error Handling
- Files that cannot be read are reported but don't stop the scan
- Error details are included in both console output and JSON reports
- Supports various file corruption scenarios

## Dependencies

- `python-docx`: For reading DOCX files
- `PyPDF2`: For reading PDF files
- `pathlib`: For cross-platform path handling (built-in)

## License

This project is open source. Feel free to modify and distribute as needed.